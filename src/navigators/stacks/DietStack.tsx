// DietStack.tsx

import React from "react";
import { Platform } from "react-native";
import { TransitionPresets } from "@react-navigation/stack";
import { createStackNavigator } from "@react-navigation/stack";

import {
  AISuggestions,
  DietTracker,
  LogFood,
  LogFoodMain,
} from "@/screens";

export type DietStackParamList = {
  DietScreen: undefined;
  AISuggestions: undefined;
  LogFood: undefined;
  LogFoodMain: undefined;
};

const Stack = createStackNavigator<DietStackParamList>();

const DietStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        // Platform-specific animations for optimal performance
        // iOS: Slide from right (native iOS feel)
        // Android: Fade from bottom (native Android feel, better performance)
        ...(Platform.OS === 'ios'
          ? TransitionPresets.SlideFromRightIOS
          : TransitionPresets.FadeFromRightAndroid
        ),
      }}
    >
      <Stack.Screen
        name="DietScreen"
        component={DietTracker}
        options={{ headerShown: false }}
      />
      <Stack.Screen name="AISuggestions" component={AISuggestions} />
      <Stack.Screen name="LogFood" component={LogFood} />
      <Stack.Screen name="LogFoodMain" component={LogFoodMain} />
    </Stack.Navigator>
  );
};

export default DietStack;

import dayjs from "dayjs";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { View, TouchableOpacity } from "react-native";
import { Card, Typography } from "@/components/atoms";
import Icons from "@/theme/assets/images/svgs/icons";
import Common from "@/theme/common.style";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import { useAppDispatch, useAppSelector } from "@/store";
import { fetchFoodEntries } from "@/store/slices/dietTrackerSlice";
import {
  fetchDailyConsumedPheAllowance,
  selectDailyPheAllowance,
} from "@/store/slices/pheAllowanceSlice";
import { useTheme } from "@/theme";
import { getPheAllowanceUnit, getPheAllowanceValue, roundNumber } from "@/utils/helpers";
import { FoodItemTransformer } from "@/utils/diet";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getPheConsumedStyles from "./PheConsumedDashboardCard.styles";
import { useSelector } from "react-redux";
import { selectConsumptionUnit, selectIsSimplifiedDiet } from "@/store/slices/settingsSlice";

interface PheConsumedDashboardCardProps {
  date: string; // ISO format date passed as a prop
}

const PheConsumedDashboardCard: React.FC<PheConsumedDashboardCardProps> = ({
  date,
}) => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { colors } = useTheme();

  const styles: any = useDynamicStyles(getPheConsumedStyles);

  const dailyPheAllowance = useAppSelector(selectDailyPheAllowance);
  const isSimplifiedDiet = useAppSelector(selectIsSimplifiedDiet);
  const [foodsData, setFoodsData] = useState<any>({});
  const [consumed, setConsumed] = useState<string>("0");
  const [allowance, setAllowance] = useState<string>("0");
  const consumptionType = useSelector(selectConsumptionUnit);

  interface LastFetched {
    formattedDate: string;
    consumptionType: string;
  }
  const lastFetchedDate = useRef<LastFetched | null>(null); // Track the last fetched date

  // Fetch and calculate foodsData based on the selected date
  const fetchEntries = useCallback(async (forceRefetch: boolean = false) => {
    // Format date using local time (not UTC)
    const formattedDate = dayjs(date)
      .endOf("day")
      .local()
      .format("YYYY-MM-DDTHH:mm:ss");

    // Skip fetch if date and consumption type haven't changed and not forcing refetch
    if (!forceRefetch && lastFetchedDate.current?.formattedDate === formattedDate && lastFetchedDate.current?.consumptionType === consumptionType) return;

    lastFetchedDate.current = {
      formattedDate,
      consumptionType
    };

    const dateISO = new Date(date).toISOString()
    // Fetch food entries and daily consumed PHE allowance
    const foodEntriesResult = await dispatch(
      fetchFoodEntries({ date: dateISO })
    ).unwrap();

    await dispatch(fetchDailyConsumedPheAllowance(dayjs(formattedDate).utc().toISOString()));

    // Calculate total PHE from fetched food entries
    const transformer = new FoodItemTransformer(foodEntriesResult || []);
    const propName = consumptionType === "Protein" ? "protein" : "phe";
    const totalPhe =
      (
        transformer.calculateTotalPHEAndProteinFromItems(undefined, undefined, isSimplifiedDiet) as {
          [propName]: { total: number };
        }
      )?.[propName]?.total?.toFixed(2) || "0";

    setFoodsData({
      consumedPhe: totalPhe || "0",
    });
  }, [date, dispatch, consumptionType, isSimplifiedDiet]);

  // Fetch data when dependencies change
  useEffect(() => {
    fetchEntries();
  }, [fetchEntries]);

  // Refetch data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Force refetch when screen regains focus to ensure data is up-to-date
      fetchEntries(true);
    }, [fetchEntries])
  );

  // Update consumed and allowance values
  useEffect(() => {
    setConsumed(
      foodsData?.consumedPhe
        ? `${roundNumber(foodsData?.consumedPhe)} ${getPheAllowanceUnit(consumptionType)}`
        : `0 ${getPheAllowanceUnit(consumptionType)}`
    );
    setAllowance(dailyPheAllowance ? `${getPheAllowanceValue(dailyPheAllowance, consumptionType)} ${getPheAllowanceUnit(consumptionType)}` : `0 ${getPheAllowanceUnit(consumptionType)}`);

  }, [foodsData?.consumedPhe, dailyPheAllowance, consumptionType]);

  const handlePress = () => {
    navigation.navigate("Diet", {
      screen: "DietScreen",
    });
  };

  return (
    <Card style={styles.container}>
      <TouchableOpacity onPress={handlePress}>
        <View style={styles.iconContainer}>
          <Icons.FoodHexagon color={colors.textPrimary} />
        </View>
        <Typography.H3 style={[styles.title, Common.textBold]}>
          {consumptionType === "Protein" ? "PRO" : "Phe"} Consumed
        </Typography.H3>
        <View style={styles.pheConsumedContainer}>
          <Typography.B1 style={Common.textBold}>{consumed}</Typography.B1>
        </View>
        <Typography.B3 style={styles.details}>
          Consumed {consumptionType === "Protein" ? "PRO" : "Phe"}: {consumed}
        </Typography.B3>
        <Typography.B3 style={styles.details}>
          Daily {consumptionType === "Protein" ? "PRO" : "Phe"} Allowance:{" "}
          {dailyPheAllowance
            ? `${getPheAllowanceValue(dailyPheAllowance, consumptionType)} ${getPheAllowanceUnit(consumptionType)}`
            : "N/A"}
        </Typography.B3>
      </TouchableOpacity>
    </Card>
  );
};

export default PheConsumedDashboardCard;

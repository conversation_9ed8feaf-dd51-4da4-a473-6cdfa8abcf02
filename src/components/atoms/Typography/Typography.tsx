import { useMemo } from "react";
import { Text, TextProps, TextStyle } from "react-native";

import { useTheme } from "@/theme";
import { getTypography } from "./Typography.style";
import { TypographyKeys } from "@/types/components/atoms/Typography";

const createTextComponent = (styleKey: TypographyKeys) => {
  return ({
    children,
    style: customStyle,
    ...props
  }: TextProps & { style?: TextStyle | TextStyle[] }) => {
    const theme = useTheme();
    const typography = getTypography(theme);

    const mergedStyles = useMemo(
      () => [typography[styleKey], customStyle],
      [typography, styleKey, customStyle]
    );

    return (
      <Text {...props} style={mergedStyles}>
        {children}
      </Text>
    );
  };
};

// Headline
export const H0 = createTextComponent("h0");
export const H1 = createTextComponent("h1");
export const H2 = createTextComponent("h2");
export const H3 = createTextComponent("h3");
export const H4 = createTextComponent("h4");
export const H5 = createTextComponent("h5");

// Body
export const B1 = createTextComponent("b1");
export const B2 = createTextComponent("b2");
export const B3 = createTextComponent("b3");
export const B4 = createTextComponent("b4");
export const B5 = createTextComponent("b5");

export default {
  H0,
  H1,
  H2,
  H3,
  H4,
  H5,
  B1,
  B2,
  B3,
  B4,
  B5,
};

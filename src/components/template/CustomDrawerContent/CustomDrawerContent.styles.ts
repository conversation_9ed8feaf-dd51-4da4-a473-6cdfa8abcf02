import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { StyleSheet } from "react-native";
import { ms } from "react-native-size-matters";

const getCustomDrawerStyle = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: ms(20),
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: ms(14),
    marginBottom: ms(20),
  },
  title: {
    fontFamily: Fonts.RALEWAY_BOLD,
  },
  profileIcon: {
    width: ms(24),
    height: ms(24),
  },
  drawerItemContainer: {
    flex: 1,
  },
  drawerItemWrapper: {
    borderBottomWidth: ms(1),
    borderBottomColor: theme.colors.borderDarkGray,
  },
  logoutButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: ms(10),
    padding: ms(15),
    marginHorizontal: ms(20),
    alignItems: "center",
    marginBottom: ms(100),
  },
  logoutButtonText: {
    fontSize: ms(16),
    textAlign: "center",
    fontFamily: Fonts.RALEWAY_SEMI_BOLD,
    color: theme.colors.white
  },
  neg_margin_12: {
    // marginLeft: -ms(4),
  },
});

export default getCustomDrawerStyle;

import toastService from "@/shared/toast.service";
import { IS_ANDROID, IS_IOS } from "@/theme/_config";
import notifee, {
  AndroidImportance,
  AuthorizationStatus,
  EventType,
} from "@notifee/react-native";
import messaging, {
  FirebaseMessagingTypes,
} from "@react-native-firebase/messaging";
import { useEffect } from "react";
import { PermissionsAndroid, Platform } from "react-native";
import { navigationRef } from "@/utils/Navigation";
import { Screens } from "@/constants";
import { store } from "@/store";
import { registerDeviceThunk } from "@/store/slices/notificationSlice";
import {
  DisablePushNotification,
  PushNotification,
} from "@/types/schemas/PushNotification";
import DeviceInfo from "react-native-device-info";
import { disableRegisterDeviceThunk } from "@/store/slices/notificationSlice";
import { v4 as uuidv4 } from "uuid"; // or use any other way to generate a unique ID

let fcmToken: string | null = null;

// Helper function to ensure device is registered for remote messages
export const ensureDeviceRegistration = async (): Promise<boolean> => {
  try {
    const isRegistered = await messaging().isDeviceRegisteredForRemoteMessages;
    if (!isRegistered) {
      console.log("Registering device for remote messages...");
      await messaging().registerDeviceForRemoteMessages();
      // Add delay to ensure registration is complete
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Verify registration
      const isNowRegistered = await messaging().isDeviceRegisteredForRemoteMessages;
      if (!isNowRegistered) {
        console.error("Device registration failed after retry");
        return false;
      }
      console.log("Device registration successful");
    }
    return true;
  } catch (error) {
    console.error("Error ensuring device registration:", error);
    return false;
  }
};

export const getFCMToken = () => {
  // Get the notification token
  console.log({ getFCMToken: fcmToken });
  return fcmToken;
};

export const removeFCMToken = async () => {
  // Get the notification token
  return messaging().deleteToken();
};

export const requestPermissionForNotification = async () => {
  try {
    if (IS_ANDROID && Number(Platform.Version) >= 33) {
      // If Android 13 or higher...
      return PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
      );
    } else if (IS_IOS) {
      console.log("Requesting iOS notification permissions...");

      // Ensure device is registered before requesting permissions
      const isRegistered = await ensureDeviceRegistration();
      if (!isRegistered) {
        console.error("Failed to register device before requesting permissions");
        return "denied";
      }

      // For iOS, first request permission from Firebase Messaging
      const authStatus = await messaging().requestPermission();
      console.log("Firebase messaging permission status:", authStatus);

      // Check if permission was granted
      if (authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL) {

        // Also request permission from notifee for local notifications
        const notifeeSettings = await notifee.requestPermission();
        console.log("Notifee permission settings:", notifeeSettings);

        // Handle both boolean (iOS 12 and below) and object (iOS 13+) responses
        if (typeof notifeeSettings === "boolean") {
          return notifeeSettings ? "granted" : "denied";
        } else {
          if (notifeeSettings.authorizationStatus === AuthorizationStatus.AUTHORIZED ||
              notifeeSettings.authorizationStatus === AuthorizationStatus.PROVISIONAL) {
            return "granted";
          } else {
            return "denied";
          }
        }
      } else {
        console.log("Firebase messaging permission denied");
        return "denied";
      }
    } else {
      // On Android < 13, POST_NOTIFICATIONS doesn’t exist, so treat as granted
      return "granted";
    }
  } catch (error) {
    console.error("Error requesting notification permission:", error);
    return "denied";
  }
};

export function useNotifee() {
  // getting permission from the device
  useEffect(() => {
    (async () => {
      console.log("useNotifee initialization started");
      try {
        // Use the helper function to ensure proper registration
        const isRegistered = await ensureDeviceRegistration();

        if (isRegistered) {
          // Only get token after ensuring registration is complete
          fcmToken = await messaging().getToken();
          console.log(`FCM Token initialized on ${Platform.OS}: ${fcmToken ? 'Success' : 'Failed'}`);
        } else {
          console.error("Device registration failed during initialization");
        }
      } catch (err) {
        console.error("FCM Registration error:", err);
        fcmToken = null;
      }
    })();
  }, []);

  // Subscribe to foreground events
  useEffect(() => {
    return notifee.onForegroundEvent(({ type, detail }) => {
      console.log("onForegroundEvent: ", { type, detail });
      switch (type) {
        case EventType.PRESS:
          console.log("🚀 ~ returnnotifee.onForegroundEvent ~ detail:", detail);
          console.log("🚀 ~ returnnotifee.onForegroundEvent ~ type:", type);
          break;
      }
    });
  }, []);

  // Create channel at startup
  useEffect(() => {
    if (IS_ANDROID) {
      createChannel();
    }
  }, []);

  async function createChannel() {
    console.log("Creating notification channel_1");
    await notifee.createChannel({
      id: "my_pillbox_channel",
      name: "Pillbox Notifications",
      sound: "pillbox",
      importance: AndroidImportance.HIGH,
    });
  }

  // return { isLoading };
}

// Note that an async function or a function that returns a Promise
// is required for both subscribers.
async function onMessageReceived(
  message: FirebaseMessagingTypes.RemoteMessage
) {
  const { sentTime, notification = {}, data } = message || {};
  const { title = "", body = "" } = notification;

  try {
    console.log("🚀 ~ onMessageReceived ~ Notification:", message);

    // Create a unique ID to prevent replacement
    // const notificationId = uuidv4(); // Ensures each notification is unique

    toastService.notify(title, body, data);
  } catch (error) {
    console.log("🚀 ~ onMessageReceived ~ Error:", error);
  }
}

export const initiateBackgroundEvent = () => {
  const routeConfig = {
    screen: "MainTabs",
    params: { screen: "Home" },
  };

  if (IS_IOS) {
    messaging().onNotificationOpenedApp(
      async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
        try {
          navigationRef.current?.navigate(Screens.App, routeConfig);
        } catch (error) {
          console.log("Navigation error:", error);
        }
      }
    );
  } else {
    messaging().setBackgroundMessageHandler(
      async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
        console.log(
          "🚀 ~ messaging ~ remoteMessageSetBackgroundMessageHandler:",
          remoteMessage
        );
        try {
          navigationRef.current?.navigate(Screens.App, routeConfig);
        } catch (error) {
          console.log("Navigation error:", error);
        }
      }
    );
  }

  messaging().onMessage(onMessageReceived);
};

export const registerUnRegisterDevice = async (
  pushNotificationEnabled: boolean
) => {
  console.log("pushNotificationEnabled", pushNotificationEnabled);
};

export const registerDeviceOnHub = async (pushNotificationEnabled: boolean) => {
  try {
    const uniqueId = await DeviceInfo.getUniqueId();
    const deviceToken = getFCMToken();

    if (!deviceToken) {
      throw new Error("FCM token is not available");
    }

    const payload: PushNotification = {
      deviceId: uniqueId,
      platform: IS_ANDROID ? "gcm" : "apns",
      deviceToken: deviceToken,
      isEnabled: pushNotificationEnabled,
    };

    return store.dispatch(registerDeviceThunk(payload));
  } catch (error) {
    console.error("Error in registerDeviceOnHub:", error);
    throw error;
  }
};

export const disableDeviceOnHub = async (pushNotificationDisabled: boolean) => {
  try {
    const deviceToken = getFCMToken();

    if (!deviceToken) {
      throw new Error("FCM token is not available");
    }

    const payload: DisablePushNotification = {
      tokenId: deviceToken,
      isEnabled: pushNotificationDisabled,
    };

    return store.dispatch(disableRegisterDeviceThunk(payload));
  } catch (error) {
    console.error("Error in disableDeviceOnHub:", error);
    throw error;
  }
};

export const getAndSetFCMToken = async (): Promise<string | null> => {
  if (fcmToken) {
    console.log("FCM Token already set:", fcmToken);
    return fcmToken;
  }
  try {
    console.log("Attempting to get FCM token...");

    // Ensure device is registered before getting token
    const isRegistered = await ensureDeviceRegistration();
    if (!isRegistered) {
      throw new Error("Failed to register device for remote messages");
    }

    console.log("Getting FCM token...");
    const token = await messaging().getToken();

    if (!token) {
      throw new Error("FCM token is null or empty");
    }

    fcmToken = token;
    console.log(`FCM Token retrieved successfully: ${token ? 'Token exists' : 'No token'}`);
    return token;
  } catch (err) {
    console.error("Failed to get FCM Token:", err);
    fcmToken = null; // Reset token on error
    return null;
  }
};
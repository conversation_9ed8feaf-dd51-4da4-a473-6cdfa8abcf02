// /store/slices/authSlice.ts

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../index'; // Adjust the import based on your project structure
import { registerDevice, disableBackgroundNotify } from '@/services/api/notificationAPI'; // Adjust the path based on your project
import { checkPushStatus } from '@/services/api/userAPI';

interface NotificationState {
	deviceToken: string;
	platform?: 'gcm' | 'apns';
	deviceId: string;
	isUserAlreadyLoggedIn: boolean;
	loading: boolean;
	error: string | null;
	isEnabled: boolean;
	toggleLoading: boolean;
	toggleNotify: boolean;
}

const initialState: NotificationState = {
	deviceId: '',
	platform: undefined,
	deviceToken: '',
	loading: false,
	isUserAlreadyLoggedIn: false,
	error: null,
	isEnabled: false,
	toggleLoading: false,
	toggleNotify: false,
};

// Async thunk for registering device notification
export const registerDeviceThunk = createAsyncThunk<void, { deviceToken: string; platform: 'gcm' | 'apns'; deviceId: string; isEnabled: boolean }>(
	'notification/registerDeviceThunk',
	async ({ deviceToken, platform, deviceId, isEnabled }) => {
		try {
			await registerDevice({ deviceToken, platform, deviceId, isEnabled });
		} catch (error) {
			console.error('Error registering device:', error);
			throw error;
		}
	}
);

export const getToggleNotify = createAsyncThunk<number, string>('notification/getToggleNotify', async (token, { rejectWithValue }) => {
	try {
		const profile = await checkPushStatus(token); // Call getUserById without passing a userId
		return profile;
	} catch (error) {
		return rejectWithValue(error);
	}
});

export const disableRegisterDeviceThunk = createAsyncThunk<void, { tokenId: string; isEnabled: boolean }>('notification/disableRegisterDeviceThunk', async ({ tokenId, isEnabled }) => {
	try {
		await disableBackgroundNotify({ tokenId, isEnabled });
	} catch (error) {
		console.error('Error Unregistering device:', error);
		throw error;
	}
});

// Slice creation
const notificationSlice = createSlice({
	name: 'notification',
	initialState,
	reducers: {
		setToggleNotify: (state, action) => {
			state.toggleNotify = action.payload;
		},
		setToggleLoader: (state, action) => {
			state.toggleLoading = action.payload;
		},
		setIsUserAlreadyLoggedIn: (state, action: PayloadAction<boolean>) => {
			state.isUserAlreadyLoggedIn = action.payload;
		},
		resetNotificationState : ()=>{
			return initialState;
		}
	},
	extraReducers: (builder) => {
		builder
			.addCase(registerDeviceThunk.pending, (state) => {
				state.toggleLoading = true;
				state.loading = true;
				state.error = null;
			})
			.addCase(registerDeviceThunk.fulfilled, (state) => {
				state.toggleLoading = false;
				state.loading = false;
				state.isUserAlreadyLoggedIn = true;
			})
			.addCase(registerDeviceThunk.rejected, (state, action) => {
				state.toggleLoading = false;
				state.loading = false;
				state.isUserAlreadyLoggedIn = false;
				state.toggleNotify = false; // Reset toggle state on error
				state.error = action.error.message ?? 'Failed to register device';
			})
			.addCase(getToggleNotify.pending, (state) => {
				state.toggleLoading = true;
			})
			.addCase(getToggleNotify.fulfilled, (state, action) => {
				state.toggleLoading = false;
				state.toggleNotify = action.payload == 1 ? true : false;
			})
			.addCase(getToggleNotify.rejected, (state, action) => {
				state.toggleLoading = false;
				state.error = action.error.message ?? 'Failed to get notification status';
			})
			.addCase(disableRegisterDeviceThunk.pending, (state) => {
				state.toggleLoading = true;
			})
			.addCase(disableRegisterDeviceThunk.fulfilled, (state) => {
				state.toggleLoading = false;
				state.toggleNotify = false;
			})
			.addCase(disableRegisterDeviceThunk.rejected, (state, action) => {
				state.toggleLoading = false;
				state.toggleNotify = true; // Reset to enabled state on disable error
				state.error = action.error.message ?? 'Failed to disable notifications';
			});
	},
});

export const { setToggleNotify, setToggleLoader, setIsUserAlreadyLoggedIn, resetNotificationState } = notificationSlice.actions;

// Selector to access notification state from the store
export const selectNotificationLoading = (state: RootState) => state.notification.loading;
export const selectNotificationError = (state: RootState) => state.notification.error;

// Export the reducer to be added to the store
export default notificationSlice.reducer;

/** @format */

import { useAuth0 } from "react-native-auth0";
import { <PERSON><PERSON>, Appearance, BackHand<PERSON>, <PERSON><PERSON>, ScrollView, TouchableOpacity, View } from "react-native";
import { useForm } from "react-hook-form";
import { useMMKV } from "react-native-mmkv";
import { useTranslation } from "react-i18next";
import { ms } from "react-native-size-matters";
import DeviceInfo from "react-native-device-info";
import * as ImagePicker from "react-native-image-picker";
import Clipboard from "@react-native-clipboard/clipboard";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useFocusEffect, useNavigation } from "@react-navigation/native";

import { useTheme } from "@/theme";
import { config } from "@/theme/_config";
import { Variant } from "@/types/theme/config";

import { useAppSelector, useAppDispatch } from "@/store";

import Common from "@/theme/common.style";
import { Typography } from "@/components/atoms";
import { SafeScreen } from "@/components/template";
import Icons from "@/theme/assets/images/svgs/icons";
import Button from "@/components/atoms/Button/Button";
import { deleteUser, getPreSignedUrl } from "@/services/api/userAPI";
import {
  compressImage,
  truncateText,
  uploadFileToAzure,
  validateEmail,
  validateName,
  validatePhoneNumber,
} from "@/utils/helpers";
import {
  fetchUserById,
  resetProfile,
  updateSimplifiedDietThunk,
  updateUserProfileByPatchThunk,
  updateUserProfileThunk,
} from "@/store/slices/userSlice";
import Header from "@/components/molecules/Header/Header";
import InfoSection from "@/components/molecules/InfoSection/InfoSection";
import ProfileHeader from "@/components/molecules/ProfileHeader/ProfileHeader";
import SettingsOption from "@/components/molecules/SettingsOption/SettingsOption";
import SettingsToggle from "@/components/molecules/SettingsToggle/SettingsToggle";
import SimplifiedDietToggle from "@/components/molecules/SimplifiedDietToggle";
import CustomTextInput from "@/components/atoms/CustomTextInput/CustomTextInput";

import { User } from "@/types/schemas/user";
import { logout } from "@/store/slices/authSlice";
import getProfileStyle from "./ProfileScreen.style";
import Loading from "@/components/atoms/Loading/Loading";
import useAppStateListener from "@/hooks/useAppStateListener";
import RNCheckbox from "@/components/atoms/RNCheckbox/RNCheckbox";
import { resetDietState } from "@/store/slices/dietTrackerSlice";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { resetDashboardState } from "@/store/slices/dashboardSlice";
import GenericModal from "@/components/molecules/GenericModal/GenericModal";
import { fetchPheAllowances, resetPheState } from "@/store/slices/pheAllowanceSlice";
import { resetOnboarding, setPrefrenceSelected } from "@/store/slices/onboardingSlice";
import { getToggleNotify, setToggleLoader, setToggleNotify } from "@/store/slices/notificationSlice";
import {
  registerDeviceOnHub,
  requestPermissionForNotification,
  disableDeviceOnHub,
  getFCMToken,
  getAndSetFCMToken,
} from "@/hooks/useNotifee";

import {
  resetSettingsState,
  selectConsumptionUnit,
  selectIsSimplifiedDiet,
  setConsumptionUnit,
  setIsSimplifiedDiet,
  setProfileTzToggle,
  updateDeviceTimeZoneThunk,
  updateTimeZoneToggleThunk,
} from "@/store/slices/settingsSlice";
import { useTimezone } from "@/hooks/useTimezone";
import { getEnvironment } from "@/utils/environment";
import TimezoneOption from "@/components/molecules/TimezoneOption";
import { resetContactState } from "@/store/slices/patientContactSlice";
import { fetchFoodDetails, selectFoodDetails } from "@/store/slices/foodSlice";
import ConsumptionToggle from "@/components/molecules/ConsumptionToggle/ConsumptionToggle";
import { TimezoneService } from "@/utils/timezoneService";

const ProfileScreen = () => {
  const mmkv = useMMKV();

  const navigation = useNavigation<any>();
  const { clearCredentials } = useAuth0();
  const { handleProfileTzToggle } = useTimezone()

  const { colors, variant, changeTheme } = useTheme();
  const styles: any = useDynamicStyles(getProfileStyle);
  const dispatch = useAppDispatch();
  // Remove timezone hook from Profile screen since we don't want modal here
  const { user: profile, loading: profileLoading } = useAppSelector((state) => state.user);
  const { loading: pheAllowanceLoading, data: pheAllowanceData } = useAppSelector((state) => state.pheAllowance);
  const { toggleLoading, toggleNotify } = useAppSelector((state) => state.notification);
  const { profileTzToggle } = useAppSelector((state) => state.settings);
  const [subscribedToUpdates, setSubscribedToUpdates] = useState<boolean>(profile?.subscribedToUpdates ?? false);
  const [dataUsageConsent, setDataUsageConsent] = useState<boolean>(profile?.dataUsageConsent ?? false);

  const scrollViewRef = useRef<ScrollView>(null);

  const keepHomeTzVisible = profile?.timeZoneId !== TimezoneService.getDeviceTimeZone();
  // Using React Hook Form for form management
  useForm<User>({
    mode: "all",
    defaultValues: {
      subscribedToUpdates: profile?.subscribedToUpdates ?? false,
      dataUsageConsent: profile?.dataUsageConsent ?? false,
    },
  });

  const openPheAllowance = useCallback(() => navigation.navigate("PheAllowance"), []);

  const handleLogout = async () => {
    setDeleteModalVisible(false); // Close the modal after logout
    dispatch(resetProfile());
    dispatch(resetPheState());
    dispatch(resetDietState());
    dispatch(resetContactState());
    dispatch(resetDashboardState());
    await clearCredentials();
    dispatch(resetOnboarding());
    dispatch(logout());
  };
  const handleDeleteAccount = useCallback(async () => {
    try {
      setDeleteModalVisible(false);
      setInternalLoading(true);
      await deleteUser();
      await handleLogout();
      dispatch(setPrefrenceSelected(false));
      dispatch(resetSettingsState());
      changeTheme("dark");
      setInternalLoading(false);
    } catch (ex) {
      console.error("Error deleting account", ex);
      setInternalLoading(false);
    }
  }, [dispatch, handleLogout]);

  const fbEmailSantizer = useCallback((email: string | undefined) => {
    if (email?.includes("@facebook")) {
      return email?.split("@")?.[0];
    }
    return email;
  }, []);

  const [name, setName] = useState(profile?.name);
  const [email, setEmail] = useState(fbEmailSantizer(profile?.email));
  const [phone, setPhone] = useState(profile?.phoneNumber);
  const [defaultImage, setDefaultImage] = useState("");
  const [internalLoading, setInternalLoading] = useState(false);
  const [avatar, setAvatar] = useState<{ uri?: string }>({});
  const [isEditing, setIsEditing] = useState(false);
  const [selectedMode, setSelectedMode] = useState("");
  const [discardModalVisible, setDiscardModalVisible] = useState(false);
  const [saveChangesModalVisible, setSaveChangesModalVisible] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const isSimplifiedDiet = useAppSelector(selectIsSimplifiedDiet);
  const foodDetails = useAppSelector(selectFoodDetails);
  const [copied, setCopied] = useState(false);
  const consumptionType = useAppSelector(selectConsumptionUnit);

  const [deleteModalVisible, setDeleteModalVisible] = useState(false);

  const toggleSaveChangesModal = () => setSaveChangesModalVisible(!saveChangesModalVisible);
  const toggleDiscardModal = () => setDiscardModalVisible(!discardModalVisible);
  const toggleIsEditing = () => setIsEditing(!isEditing);

  useAppStateListener(undefined, () => {
    setDeleteModalVisible(false);
  });

  useEffect(() => {
    dispatch(fetchFoodDetails());
  }, [dispatch]);

  useEffect(() => {
    let uiMode = mmkv.getString("theme");
    if (!uiMode) {
      mmkv.set("theme", "dark");
      uiMode = "dark";
      Appearance.setColorScheme("dark");
    }
    setSelectedMode(uiMode);
  }, [selectedMode]);

  useEffect(() => {
    dispatch(fetchPheAllowances());
    dispatch(getToggleNotify(getFCMToken() || ""));
  }, [dispatch]);

  useFocusEffect(
    useCallback(() => {
      dispatch(fetchUserById());
      // Remove timezone initialization from Profile screen

      // Reset scroll position to top when screen is focused
      scrollViewRef.current?.scrollTo({ y: 0, animated: false });
    }, [])
  );

  const { t } = useTranslation(["onBoardingUser"]);
  const { t: pt } = useTranslation(["profileScreen"]);

  const toggleDeleteModal = useCallback(() => {
    setDeleteModalVisible(!deleteModalVisible);
  }, [deleteModalVisible]);

  useEffect(() => {
    async function signedImage() {
      if (profile?.profilePictureUrl) {
        try {
          const response = await getPreSignedUrl(profile?.profilePictureUrl);
          setDefaultImage(response);
        } catch (e) {
          return "";
        }
      }
    }

    signedImage();
  }, [profile?.profilePictureUrl]);

  /* -----------------------------------------------------------------------
   *  BACK‑HANDLER (registered once – updates only when the deps that matter
   *  for the handler change)
   * ----------------------------------------------------------------------- */
  useEffect(() => {
    const backAction = () => {
      const hasChanges = name !== profile?.name ||
        email !== profile?.email ||
        phone !== profile?.phoneNumber ||
        !!avatar?.uri;

      if (isEditing && hasChanges) {
        toggleDiscardModal();
        return true; // prevent default back navigation
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, [
    name,
    email,
    phone,
    avatar,
    isEditing,
    profile,
    toggleDiscardModal, // now stable, won't cause a re‑run each render
  ]);

  /* -----------------------------------------------------------------------
   *  TIME‑ZONE SYNC – runs only when the data it watches actually changes
   * ----------------------------------------------------------------------- */
  useEffect(() => {
    const asyncTzUpdates = async () => {
      if (!keepHomeTzVisible && profile?.timeZoneToggle) {
        // turn OFF the "keep home TZ" flag if it shouldn't be visible
        await dispatch(updateTimeZoneToggleThunk(false)).unwrap();
      }
      if (profile?.deviceTimeZone !== TimezoneService.getDeviceTimeZone()) {
        await dispatch(updateDeviceTimeZoneThunk()).unwrap();
      }
    };
    asyncTzUpdates();
  }, [
    keepHomeTzVisible,
    profile?.timeZoneToggle,
    profile?.deviceTimeZone,
    dispatch,
  ]);

  const handleBackPress = () => {
    const hasChanges =
      name !== profile?.name || email !== profile?.email || phone !== profile?.phoneNumber || !!avatar?.uri;

    if (isEditing && hasChanges) {
      toggleDiscardModal();
    } else if (isEditing) {
      toggleIsEditing();
    } else {
      navigation.goBack();
    }
  };

  const handleDiscardChanges = () => {
    setAvatar({});
    setName(profile?.name);
    setEmail(profile?.email);
    setPhone(profile?.phoneNumber);
    setIsEditing(false);
    toggleDiscardModal();
    setErrors({ name: "", email: "", phone: "" });
  };

  const handleSaveChanges = async () => {
    try {
      setInternalLoading(true);
      await dispatch(
        updateUserProfileThunk({
          name,
          phoneNumber: phone,
          subscribedToUpdates,
          dataUsageConsent,
          isSimplifiedDiet: isSimplifiedDiet, // Save simplified diet preference
        })
      );
      await dispatch(fetchUserById());
    } catch (error) {
      console.error("❌ Error saving profile changes:", error);
    } finally {
      setInternalLoading(false);
      setIsEditing(false);
      toggleSaveChangesModal();
    }
  };

  const handleImageEdit = () => {
    ImagePicker.launchImageLibrary({ mediaType: "photo" }, async (response) => {
      if (response.didCancel) return;
      if (response.errorMessage) return;

      if (response.assets && response.assets.length > 0) {
        const selectedImage = response.assets[0];
        setAvatar(selectedImage);

        try {
          const response = await getPreSignedUrl(selectedImage?.fileName || "DefaultName.jpg");
          await handleProfileUpdate(selectedImage, response);
        } catch (error) {
          console.error("❌ Error getting pre-signed URL:", error);
        }
      }
    });
  };

  const handleProfileUpdate = async (image: any, preSignedUrl: string) => {
    if (!image || !preSignedUrl) return;

    setIsUploading(true); // Start loader

    try {
      // Compress the image
      const compressedUri = await compressImage(image.uri, 0.4);
      const compressedAvatar = { ...image, uri: compressedUri };

      // Upload file
      await uploadFileToAzure(compressedAvatar, preSignedUrl)
        .then(() => {
          dispatch(updateUserProfileByPatchThunk({ url: image?.fileName }));
        })
        .catch((error) => {
          console.error("❌ File upload failed:", error.message);
        });
    } catch (compressionError) {
      console.error("❌ Image compression failed:", compressionError);
    } finally {
      setIsUploading(false); // Stop loader
    }
  };

  const [errors, setErrors] = useState({ name: "", email: "", phone: "" });

  const handleUIModeChange = (mode: string) => {
    setSelectedMode(mode);
    changeTheme(mode as Variant);
  };

  const handleNameChange = (text: string) => {
    setName(text);
    setErrors((prev) => ({ ...prev, name: validateName(text) }));
  };

  const handleEmailChange = (text: string) => {
    setEmail(text);
    setErrors((prev) => ({ ...prev, email: validateEmail(text) }));
  };

  const handlePhoneChange = (text: string) => {
    setPhone(text);
    setErrors((prev) => ({ ...prev, phone: validatePhoneNumber(text) }));
  };

  const showNotificationAlert = () => {
    Alert.alert(
      "Enable Notifications",
      "Notifications are currently disabled. Would you like to enable them in the settings?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Go to Settings",
          onPress: () => {
            Linking.openSettings();
          },
        },
      ],
      { cancelable: false }
    );
  };

  const handlePushNotificationToggle = async (value: boolean) => {
    try {
      dispatch(setToggleLoader(true));

      if (value) {
        // Request permission first
        const isGranted = await requestPermissionForNotification();
        if (isGranted === "granted") {
          // Get FCM token
          const fcm = await getAndSetFCMToken();
          if (fcm) {
            // Register device and wait for completion
            await registerDeviceOnHub(value);
            dispatch(setToggleNotify(true));
          } else {
            // FCM token failed - reset toggle state
            dispatch(setToggleNotify(false));
            console.error("Failed to get FCM token");
          }
        } else {
          // Permission denied - reset toggle state and show alert
          dispatch(setToggleNotify(false));
          showNotificationAlert();
        }
      } else {
        // Disable notifications
        await disableDeviceOnHub(value);
        dispatch(setToggleNotify(false));
      }
    } catch (error) {
      // Handle any errors - reset toggle state
      console.error("Error handling push notification toggle:", error);
      dispatch(setToggleNotify(false));
    } finally {
      // Always reset loading state
      dispatch(setToggleLoader(false));
    }
  };

  // Handler for simplified diet toggle
  const handleSimplifiedDietToggle = (value: boolean) => {
    dispatch(setIsSimplifiedDiet(value));
    dispatch(updateSimplifiedDietThunk({ isSimplifiedDiet: value }));
  };

  const getSeletedStyle = useCallback(
    (mode: string, currentMode: string, style: string) => {
      if (mode === currentMode) {
        return styles[style];
      }
      return {};
    },
    [styles]
  );

  return (
    <SafeScreen>
      <View style={styles.container}>
        <Header
          title="Profile"
          isEditing={isEditing}
          onEditPress={toggleIsEditing}
          onBackPress={handleBackPress}
          isDark={variant === "dark"}
        />
        <ScrollView ref={scrollViewRef} showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
          <ProfileHeader
            name={name || ""}
            imageUrl={avatar?.uri || defaultImage}
            isEditing={isEditing}
            onEditPress={handleImageEdit}
            isUpdating={isUploading}
          />

          {isEditing ? (
            <View style={styles.editContainer}>
              <View>
                <Typography.B2>Name</Typography.B2>
                <CustomTextInput value={name} onChangeText={handleNameChange} errorMessage={errors.name} />
              </View>

              <View>
                <Typography.B2>Email</Typography.B2>
                <CustomTextInput
                  value={email}
                  onChangeText={handleEmailChange}
                  keyboardType="email-address"
                  errorMessage={errors.email}
                  disabled
                />
              </View>

              <View>
                <Typography.B2>Phone Number</Typography.B2>
                <CustomTextInput
                  value={phone}
                  maxLength={20}
                  keyboardType="phone-pad"
                  onChangeText={handlePhoneChange}
                  errorMessage={errors.phone}
                />
              </View>

              <RNCheckbox
                label={t("futureUpdates")}
                value={subscribedToUpdates}
                onSelect={(val: boolean) => setSubscribedToUpdates(val)}
                checkedfillColor={colors.primary}
              />

              <RNCheckbox
                label={t("userDataInsights")}
                value={dataUsageConsent}
                onSelect={(val: boolean) => setDataUsageConsent(val)}
                errorStyle={{ marginTop: -2, marginLeft: 32 }}
                checkedfillColor={colors.primary}
              />
            </View>
          ) : (
            <View style={styles.content}>
              <InfoSection
                title="General"
                items={[
                  { icon: "person", text: name || "" },
                  { icon: "mail", text: email || "" },
                  { icon: "phone", text: phone || "" },
                ]}
              />

              <ConsumptionToggle selected={consumptionType} onSelect={(type) => dispatch(setConsumptionUnit(type))} />

              <View style={styles.settingsContainer}>
                <SettingsOption
                  consumptionType={consumptionType}
                  title={consumptionType === "Phe" ? "Phe Allowance" : "PRO Allowance"}
                  rightIcon={<Icons.Pencil width={ms(12)} height={ms(12)} color={colors.textPrimary} />}
                  onPress={openPheAllowance}
                  tagInfo={{
                    amount: pheAllowanceData?.[0]?.amount || 0,
                    unit: pheAllowanceData?.[0]?.unit || "",
                  }}
                />
              </View>

              <SimplifiedDietToggle
                isToggleOn={isSimplifiedDiet}
                onToggle={handleSimplifiedDietToggle}
                disabled={false}
                loading={false}
              />

              <View style={styles.settingsContainer}>
                <TimezoneOption isTimeOut={true} />
              </View>

              {!keepHomeTzVisible ? null : <SettingsToggle
                disabled={toggleLoading}
                loading={toggleLoading}
                title="common:timezone.keepTz"
                onToggle={handleProfileTzToggle}
                isToggleOn={profileTzToggle || false}
                description="common:timezone.keepRoutineDescription"
              />}

              <View style={styles.settingsContainer}>
                <SettingsOption title="UI Mode" disabled />

                <View style={styles.accordionContainer}>
                  <Button.Outline
                    onPress={() => handleUIModeChange("dark")}
                    style={[styles.modeButton, getSeletedStyle("dark", selectedMode, "selectedButton")]}
                  >
                    <Typography.B2
                      style={[styles.buttonOutlineText, getSeletedStyle("dark", selectedMode, "selectedButtonText")]}
                    >
                      Dark
                    </Typography.B2>
                  </Button.Outline>
                  <Button.Outline
                    onPress={() => handleUIModeChange("light")}
                    style={[styles.modeButton, getSeletedStyle("light", selectedMode, "selectedButton")]}
                  >
                    <Typography.B2
                      style={[styles.buttonOutlineText, getSeletedStyle("light", selectedMode, "selectedButtonText")]}
                    >
                      Light
                    </Typography.B2>
                  </Button.Outline>
                  <Button.Outline
                    onPress={() => handleUIModeChange("default")}
                    style={[styles.modeButton, getSeletedStyle("default", selectedMode, "selectedButton")]}
                  >
                    <Typography.B2
                      style={[styles.buttonOutlineText, getSeletedStyle("default", selectedMode, "selectedButtonText")]}
                    >
                      System
                    </Typography.B2>
                  </Button.Outline>
                </View>
              </View>
              <SettingsToggle
                title="Push Notifications"
                isToggleOn={toggleNotify || false}
                onToggle={handlePushNotificationToggle}
                disabled={toggleLoading}
                loading={toggleLoading}
                description="pushnotification:pushnotification.description"
              />

              <View
                style={{
                  ...styles.deleteAccountButtonContainer,
                  flexDirection: "column",
                }}
              >
                <View
                  style={{
                    width: "100%",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <SettingsOption title="Delete Account" disabled />

                  <Button.Outline
                    onPress={() => toggleDeleteModal()}
                    style={[styles.modeButton, { borderColor: colors.delete, right: ms(10) }]}
                  >
                    <Typography.B2
                      style={[
                        styles.buttonOutlineText,

                        getSeletedStyle("default", selectedMode, "selectedButtonText"),
                        { color: colors.whiteBlack },
                      ]}
                    >
                      Delete
                    </Typography.B2>
                  </Button.Outline>
                </View>
                <View style={{ paddingHorizontal: ms(25), paddingBottom: ms(10) }}>
                  <View style={styles.descriptionContainer}>
                    <Typography.B2 style={{ ...styles.disclaimer, textAlign: "left" }}>
                      {pt("profile.deleteDescription")}
                    </Typography.B2>
                  </View>
                </View>

                <GenericModal
                  isVisible={deleteModalVisible}
                  onClose={toggleDeleteModal}
                  onConfirm={handleDeleteAccount}
                  headerText={"Delete Account"}
                  bodyText={"Are you sure you want to delete account?"}
                  confirmText={"Confirm"}
                  closeText={"Cancel"}
                />
              </View>

              {/* Food Details Section */}

              <View style={styles.settingsContainer}>
                {foodDetails ? (
                  <View style={styles.foodDetailsContainer}>
                    {/* Toast */}
                    {copied && (
                      <View
                        style={{
                          position: "absolute",
                          top: -ms(36),
                          left: 0,
                          right: 0,
                          alignItems: "center",
                          zIndex: 10,
                        }}
                      >
                        <View style={styles.toastMessage}>
                          <Typography.B2 style={{ color: "#fff", fontWeight: "bold" }}>Copied!</Typography.B2>
                        </View>
                      </View>
                    )}
                    {/* Build/version row */}
                    <Typography.B3
                      selectable
                      style={styles.foodDetailsText}
                      numberOfLines={1}
                      lineBreakMode="tail"
                      ellipsizeMode="tail"
                    >
                      {truncateText(
                        `${DeviceInfo.getVersion() || ""}:${getEnvironment() || ""}:${foodDetails.version || ""}:${foodDetails.database_State.last_Known_Hash || ""}`,
                        50
                      )}
                    </Typography.B3>
                    <TouchableOpacity
                      onPress={() => {
                        const versionString = `${DeviceInfo.getVersion() || ""}:${getEnvironment() || ""}:${foodDetails.version || ""}:${foodDetails.database_State.last_Known_Hash || ""}`;
                        Clipboard.setString(versionString);
                        setCopied(true);
                        setTimeout(() => setCopied(false), 1500);
                      }}
                    >
                      <Icons.CopyFile
                        width={ms(11)}
                        height={ms(13)}
                        color={variant === "dark" ? colors.white : colors.textPrimary}
                        style={styles.foodDetailsIcon}
                      />
                    </TouchableOpacity>
                  </View>
                ) : null}
              </View>
            </View>
          )}

          {/* Stick Save Changes Button to Bottom */}
          {isEditing && (
            <View style={styles.bottomButtonContainer}>
              <Button.Main
                disabled={Object.values(errors).some((error) => error) || isUploading} // Disable button if there are errors
                onPress={handleSaveChanges}
                style={styles.mainBtn}
              >
                <Typography.B1 style={[Common.textBold, { color: config.colors.white }]}>Save Changes</Typography.B1>
              </Button.Main>
            </View>
          )}
        </ScrollView>
      </View>

      <GenericModal
        isVisible={saveChangesModalVisible}
        onConfirm={toggleSaveChangesModal}
        headerText={"Changes Saved"}
        bodyText={"Your profile information has been updated successfully."}
        confirmText={"OK"}
      />

      <GenericModal
        isVisible={discardModalVisible}
        onClose={toggleDiscardModal}
        onConfirm={handleDiscardChanges}
        headerText={"Discard Changes"}
        bodyText={"Are you sure you want to discard your changes?"}
        confirmText={"Yes"}
        closeText={"No"}
      />

      {internalLoading ? <Loading /> : null}
    </SafeScreen>
  );
};

export default ProfileScreen;
